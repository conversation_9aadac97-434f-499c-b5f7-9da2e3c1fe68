package com.tripudiotech.migration.validation;

import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.validation.FileImportValidator;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@QuarkusTest
class FileImportValidatorTest {

    @Inject
    FileImportValidator fileImportValidator;

    @Mock
    private FileUpload fileUpload;

    @BeforeEach
    void setUp() {
        when(fileUpload.fileName()).thenReturn("test.csv");
        when(fileUpload.size()).thenReturn(1000L);
        when(fileUpload.uploadedFile()).thenReturn(Path.of("/tmp/test.csv"));
    }

    @Test
    void testValidation_NullRequest() {
        // Test null request validation
        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", null);
        });
    }

    @Test
    void testValidation_NullFile() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(null)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_NullImportType() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(null)
                .entityType("Product")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_SingleDataType_MissingEntityType() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType(null)
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_MultipleDataTypes_MissingDataTypeColumn() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.MULTIPLE_DATA_TYPES)
                .entityType("Product")
                .dataTypeColumn(null)
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_EmptyJson() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[]")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_MalformedJson() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("{invalid json")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_MissingColumnName() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[{\"target\":\"name\",\"type\":\"ATTRIBUTE\"}]")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_DuplicateColumns() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[" +
                    "{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"ATTRIBUTE\"}," +
                    "{\"columnName\":\"name\",\"target\":\"description\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_UserIdWithoutFromExistingEntity() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_InvalidFromExistingEntityFormat() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"COMPANY\",\"target\":\"invalidformat\",\"type\":\"FROM_EXISTING_ENTITY\"}" +
                    "]")
                .build();

        assertThrows(IllegalArgumentException.class, () -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }

    @Test
    void testValidation_ValidDataMapping_UserImport() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"COMPANY\",\"target\":\"Company.name\",\"type\":\"FROM_EXISTING_ENTITY\"}," +
                    "{\"columnName\":\"FIRST_NAME\",\"target\":\"firstName\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        // This should not throw an exception
        assertDoesNotThrow(() -> {
            fileImportValidator.validateUploadRequest("tenant1", request);
        });
    }
}
