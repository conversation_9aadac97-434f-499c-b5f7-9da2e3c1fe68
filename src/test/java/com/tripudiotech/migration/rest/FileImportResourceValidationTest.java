package com.tripudiotech.migration.rest;

import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import jakarta.inject.Inject;
import jakarta.validation.Validator;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@QuarkusTest
class FileImportResourceValidationTest {

    @Inject
    FileImportResource fileImportResource;

    @InjectMock
    FileImportService fileImportService;

    @InjectMock
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @InjectMock
    Validator validator;

    @Mock
    private FileUpload fileUpload;

    @Mock
    private UserInformation userInformation;

    @BeforeEach
    void setUp() {
        when(fileUpload.fileName()).thenReturn("test.csv");
        when(fileUpload.size()).thenReturn(1000L);
        when(fileUpload.uploadedFile()).thenReturn(Path.of("/tmp/test.csv"));
        
        when(userInformation.getReferenceId()).thenReturn("user123");
        when(userInformation.getEmail()).thenReturn("<EMAIL>");
        
        when(securityProviderServiceFactory.getDefaultAuthenticateService()
                .getCurrentUserInformation()).thenReturn(userInformation);
    }

    @Test
    void testValidation_NullRequest() {
        // Test null request validation
        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(null);
        });
    }

    @Test
    void testValidation_NullFile() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(null)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_NullImportType() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(null)
                .entityType("Product")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_SingleDataType_MissingEntityType() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType(null)
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_MultipleDataTypes_MissingDataTypeColumn() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.MULTIPLE_DATA_TYPES)
                .entityType("Product")
                .dataTypeColumn(null)
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_EmptyJson() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_MalformedJson() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("{invalid json")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_MissingColumnName() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[{\"target\":\"name\",\"type\":\"ATTRIBUTE\"}]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_InvalidObjectType() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"INVALID_TYPE\"}]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_DuplicateColumns() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("Product")
                .dataMapping("[" +
                    "{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"ATTRIBUTE\"}," +
                    "{\"columnName\":\"name\",\"target\":\"description\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_UserIdWithoutFromExistingEntity() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"NAME\",\"target\":\"name\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_InvalidDataMapping_InvalidFromExistingEntityFormat() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"COMPANY\",\"target\":\"invalidformat\",\"type\":\"FROM_EXISTING_ENTITY\"}" +
                    "]")
                .build();

        assertThrows(BadRequestException.class, () -> {
            fileImportResource.upload(request);
        });
    }

    @Test
    void testValidation_ValidDataMapping_UserImport() {
        UploadFormDataRequest request = UploadFormDataRequest.builder()
                .file(fileUpload)
                .importType(ImportType.SINGLE_DATA_TYPE)
                .entityType("User")
                .dataMapping("[" +
                    "{\"columnName\":\"EMAIL\",\"target\":\"email\",\"type\":\"USER_ID\"}," +
                    "{\"columnName\":\"COMPANY\",\"target\":\"Company.name\",\"type\":\"FROM_EXISTING_ENTITY\"}," +
                    "{\"columnName\":\"FIRST_NAME\",\"target\":\"firstName\",\"type\":\"ATTRIBUTE\"}" +
                    "]")
                .build();

        // This should not throw an exception
        assertDoesNotThrow(() -> {
            // We need to mock the service call since we're only testing validation
            when(fileImportService.uploadFile(any(), any(), any())).thenReturn(null);
            fileImportResource.upload(request);
        });
    }
}
