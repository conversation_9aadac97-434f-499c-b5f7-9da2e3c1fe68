# Default profile , might use for production
application:
  service-instance-id: ${SERVICE_INSTANCE_ID:migration-service}
  keycloakServerUri: ${KEYCLOAK_SERVER_URL:https://identity-stage.glideyoke.com}
  kafka:
    schema-registry-url: ${KAFKA_SCHEMA_REGISTRY_URL:http://localhost:8081}
  datalib:
    enabled: false
  notification:
    template:
      fileImportStatusChanged: ${FILE_IMPORT_STATUS_CHANGED_NOTIFY_TEMPLATE:file-import-status-changed}
  flyway:
    enabled: true # should disable it in prod and run DDL manually
  fileImport:
    notification:
      enabled: ${NOTIFY_FILE_IMPORT:false}
    jobTriggered:
      every: ${FILE_IMPORT_TRIGGER_SECONDS:60s}
      enabled: ${FILE_IMPORT_JOB_ENABLED:false}
    processing:
      eventDriven: ${FILE_IMPORT_EVENT_DRIVEN:true}
      rowThreshold: ${FILE_IMPORT_ROW_THRESHOLD:100}
    check:
      interval: 20s
storage:
  default-provider: ${DEFAULT_STORAGE_PROVIDER:MINIO}
  default-max-size: ${DEFAULT_MAX_FILE_SIZE:**********} # 1 GB - 1024 * 1024 * 1024
  default-supported-extensions: ${SUPPORTED_EXTENSIONS:csv,xls,xlsx}
  region: ${STORAGE_REGION:us-east-2}
  endpoint: ${STORAGE_URL:http://localhost:9000}
  accessKeyId: ${STORAGE_ACCESSKEY:minio}
  secretAccessKey: ${STORAGE_SECRET:miniosecret} # Override it in production
  bucket: ${STORAGE_BUCKET:plm-file-storage-dev}
  expireTimeInMinutes: ${PRESIGNED_DOWNLOAD_EXPIRED_MINUTES:10}

cloud:
  storage:
    minio:
      enabled: true
      region: ${STORAGE_REGION:us-east-2}
      endpoint: ${STORAGE_URL:http://localhost:9000
      accessKeyId: ${STORAGE_ACCESSKEY:minio} # if you dont define the value, we will get it from serviceaccount iam role
      secretAccessKey: ${STORAGE_SECRET:miniosecret} # if you dont define the value, we will get it from serviceaccount iam role
      bucket: plm-file-storage-dev

## Kafka configuration
kafka:
  bootstrap:
    servers: ${KAFKA_BOOTSTRAP_SERVER:localhost:9092} #config kafka port in production

# Configure Kafka channels
mp:
  messaging:
    outgoing:
      import-batch-out:
        schema:
          registry:
            url: ${application.kafka.schema-registry-url} # Override it in production
        connector: smallrye-kafka
        topic: import-batch-events
        value:
          serializer: io.quarkus.kafka.client.serialization.JsonbSerializer
        failure-strategy: dead-letter-queue
    incoming:
      import-status-in:
        connector: smallrye-kafka
        topic: import-status-events
        auto.offset.reset: earliest
        enable.auto.commit: false
        concurrency: ${IMPORT_CONCURRENCY:5}
        group.id: migration-service-import-processor
        failure-strategy: dead-letter-queue
        retry-attempts: 3
        retry-max-delay: 1000
        dead-letter-queue:
          topic: import-status-events-dlq
          key.serializer: org.apache.kafka.common.serialization.StringSerializer
          value.serializer: io.quarkus.kafka.client.serialization.JsonbDeserializer
      import-status-in-dlq:
        connector: smallrye-kafka
        topic: import-status-events-dlq
        auto.offset.reset: earliest
        enable.auto.commit: false
        group.id: migration-service-dlq-processor
        value.deserializer: io.quarkus.kafka.client.serialization.JsonbDeserializer

quarkus:
  devservices:
    enabled: false
  otel:
    traces:
      exporter: none
  hibernate-orm:
    log:
      sql: true
  flyway:
    migrate-at-start: false # we disabled auto run flyway and override the configure in Flyway service - due to reactive library conflict
  live-reload:
    instrumentation: true
  datasource:
    db-kind: ${DB_TYPE:postgresql}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    jdbc: false
    reactive:
      url:  ${DB_SERVER:postgresql://localhost:5432/migration}
  application:
    version: 0.0.1
  app:
    name: migration-mservice
  oidc:
    authentication:
      cookie-domain: ${COOKIE_DOMAIN:glideyoke.com}
    internalClientSecret: ${INTERNAL_CLIENT_SECRET:MGLxMiU13MBtO8dQPUFMaLaO7ZV3ql9a}
    # oAuth login for internal communication
    internalClientId: ${INTERNAL_CLIENT_ID:glide-service-admin}
  smallrye-openapi:
    security-scheme: jwt
    security-scheme-name: apiToken
  swagger-ui:
    urls:
      default: ${QUARKUS_SWAGGER_UI_URLS_DEFAULT:/q/openapi}
    urls-primary-name: default
    path: /api
    always-include: true
    theme: material
  http:
    limits:
      max-form-attribute-size: ${DEFAULT_MAX_FILE_SIZE:1073751824}
      max-body-size: ${DEFAULT_MAX_FILE_SIZE:1073751824}

    port: ${QUARKUS_PORT:8091}
    cors:
      ~: true
      origins: "*"
  log:
    level: INFO
    category:
      com.tripudiotech:
        min-level: ${LOGGING_SEVERITY_MIN_LEVEL:TRACE}
        level: ${LOGGING_SEVERITY_LEVEL:DEBUG}
    console:
      json: ${ENABLE_JSON_CONSOLE_LOG:false}
      format: |
        - %d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}[%c{2.}] (%t) %s%e%n
  opentelemetry:
  enabled: false
  tracer:
    exporter:
      otlp:
        endpoint: http://localhost:4317  # Ensure the collector is running on this port
  scheduler:
    enabled: true


entity-api/mp-rest/uri: ${ENTITY_SERVICE_URL:https://api-stage.glideyoke.com/entity/}
asset-service-api/mp-rest/uri: ${ASSET_SERVICE_URL:http://localhost:8082}
tracking-service-api/mp-rest/uri: ${TRACKING_SERVICE_URL:http://localhost:8085}
auth-service-api/mp-rest/uri: ${AUTH_SERVICE_URL:https://auth-stage.glideyoke.com}
schema-manager-api/mp-rest/uri: ${SCHEMA_MANAGER_URL:https://api-stage.glideyoke.com/schema-manager/}

# Import service configuration
import:
  batch:
    size: ${IMPORT_BATCH_SIZE:5}
  max:
    retries: ${IMPORT_MAX_RETRY:5}
  concurrency: ${IMPORT_CONCURRENCY:5}