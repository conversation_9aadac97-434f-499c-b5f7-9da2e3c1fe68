package com.tripudiotech.migration.consumer;

import com.tripudiotech.base.event.ImportStatusEvent;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImportError;
import com.tripudiotech.migration.entity.FileImportSuccess;
import com.tripudiotech.migration.repository.FileImportRepository;
import com.tripudiotech.migration.service.FileImportErrorService;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.migration.service.FileImportSuccessService;
import com.tripudiotech.migration.util.NotificationUtil;
import com.tripudiotech.migration.util.RetryUtil;
import com.tripudiotech.migration.util.UniSemaphore;
import com.tripudiotech.migration.util.UniSemaphoreImpl;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Slf4j
@ApplicationScoped
public class ImportStatusHandler {

    private static final String LOG_PREFIX = "[ImportStatusHandler]";

    @Inject
    FileImportRepository fileImportRepository;

    @Inject
    FileImportSuccessService fileImportSuccessService;

    @Inject
    FileImportErrorService fileImportErrorService;

    @Inject
    NotificationUtil notificationUtil;

    @ConfigProperty(name = "import.retry.max-attempts", defaultValue = "3")
    int maxRetryAttempts;

    /**
     * Updates the import status based on the provided event, processing success and failure records
     * and checking for import completion. Ensures idempotency and handles retries for transient failures.
     *
     * @param event The import status event containing file import ID, batch number, and records.
     * @return A Uni representing the completion of the operation.
     */
    @WithTransaction
    public Uni<Void> updateImportStatus(ImportStatusEvent event) {
        Long fileImportId = event.getFileImportId();
        int batchNumber = event.getBatchNumber();

        // First check if this event was already processed (idempotency check)
        return checkEventProcessed(event)
                .flatMap(alreadyProcessed -> {
                    if (alreadyProcessed) {
                        log.info("{} ImportId {} batchNumber {} Event already processed",
                                LOG_PREFIX, fileImportId, event.getBatchNumber());
                        return Uni.createFrom().voidItem();
                    }

                    // Process success and failure records
                    Uni<Integer> saveSuccessUni = RetryUtil.withRetry(
                                    () -> processSuccessRecords(event, fileImportId), maxRetryAttempts)
                            .onFailure().invoke(e -> log.error(
                                    "{} ImportId {} batchNumber {} Failed to process success records",
                                    LOG_PREFIX, fileImportId, batchNumber, e));

                    Uni<Integer> saveErrorUni = RetryUtil.withRetry(
                                    () -> processFailureRecords(event, fileImportId), maxRetryAttempts)
                            .onFailure().invoke(e -> log.error(
                                    "{} ImportId {} batchNumber {} Failed to process failure records",
                                    LOG_PREFIX, fileImportId, batchNumber, e));

                    return Uni.combine().all().unis(saveSuccessUni, saveErrorUni).asTuple()
                            .flatMap(updated -> checkImportCompletion(event));
                });
    }

    protected Uni<Boolean> checkEventProcessed(ImportStatusEvent event) {
        return fileImportSuccessService.countByFileImportIdAndBatchNumber(event.getFileImportId(), event.getBatchNumber())
                .flatMap(successCount -> {

                    int expectedFailed = Optional.ofNullable(event.getFailures()).map(List::size).orElse(0);
                    int expectedSuccess = Optional.ofNullable(event.getSuccesses()).map(List::size).orElse(0);

                    log.info("ImportId {} batchNumber {} expectedFailed {} expectedSuccess {} successCount {} ", event.getFileImportId(), event.getBatchNumber(),
                            expectedFailed, expectedSuccess, successCount);

                    int expected = expectedFailed + expectedSuccess;

                    if (expectedSuccess > 0 && successCount >= expectedSuccess) {
                        return Uni.createFrom().item(true);
                    }

                    return fileImportErrorService.countByFileImportIdAndBatchNumber(event.getFileImportId(), event.getBatchNumber())
                            .map(errorCount -> ((errorCount) >= expectedFailed) && (successCount + errorCount) >= expected);
                });
    }


    @WithTransaction
    public Uni<Void> checkImportCompletion(ImportStatusEvent event) {
        Long fileImportId = event.getFileImportId();
        int batchNumber = event.getBatchNumber();

        return RetryUtil.withRetry(() -> {
                    UniSemaphore mutex = new UniSemaphoreImpl(1);
                    Uni<FileImport> fileImportUni = mutex.protect(() -> fileImportRepository.findByIdReadOnly(fileImportId));
                    Uni<Long> successCountUni = mutex.protect(() -> fileImportSuccessService.countByFileImportId(fileImportId));
                    Uni<Long> errorCountUni = mutex.protect(() -> fileImportErrorService.countByFileImportId(fileImportId));

                    return Uni.combine().all().unis(fileImportUni, successCountUni, errorCountUni).asTuple()
                            .flatMap(tuple -> {
                                FileImport fileImport = tuple.getItem1();
                                long successCount = tuple.getItem2();
                                long errorCount = tuple.getItem3();

                                if (fileImport == null) {
                                    log.info("{} ImportId {} batchNumber {} No file import found",
                                            LOG_PREFIX, fileImportId, batchNumber);
                                    return Uni.createFrom().voidItem();
                                }

                                if (fileImport.getStatus() == FileImport.Status.COMPLETED) {
                                    log.info("{} ImportId {} batchNumber {} already completed",
                                            LOG_PREFIX, fileImportId, batchNumber);
                                    return Uni.createFrom().voidItem();
                                }

                                long totalProcessed = successCount + errorCount;
                                long totalExpected = fileImport.getTotalRows();

                                if (totalProcessed > Integer.MAX_VALUE || successCount > Integer.MAX_VALUE || errorCount > Integer.MAX_VALUE) {
                                    return Uni.createFrom().failure(new IllegalStateException("Row counts exceed maximum allowed value"));
                                }

                                log.info("{} ImportId {} batchNumber {} counts - Success: {}, Errors: {}, Total Processed: {}, Expected: {}",
                                        LOG_PREFIX, fileImportId, batchNumber, successCount, errorCount, totalProcessed, totalExpected);

                                if (totalProcessed == totalExpected) {

                                    log.info("{} ImportId {} batchNumber {} Import completed, finalizing with successCount {} failedCount {} executionTime {}",
                                            LOG_PREFIX, fileImportId, batchNumber, successCount, errorCount,
                                            fileImport.getStartTime() != null ? Duration.between(fileImport.getStartTime(), LocalDateTime.now()).getSeconds() : 0);

                                    return fileImportRepository.markImportAsCompleted(
                                            fileImportId,
                                            (int) totalProcessed,
                                            (int) successCount,
                                            (int) errorCount
                                    ).flatMap(updated -> {
                                        if (updated > 0) {
                                            return fileImportRepository.findByIdReadOnly(fileImportId)
                                                    .flatMap(completedImport ->
                                                            sendNotification(
                                                                    completedImport,
                                                                    fileImport.getStatus().name(),
                                                                    FileImport.Status.COMPLETED.name()
                                                            )
                                                    );
                                        }
                                        return Uni.createFrom().voidItem();
                                    });
                                } else if (totalProcessed > totalExpected) {
                                    log.error("{} ImportId {} batchNumber {} Invalid state: Processed {} exceeds Expected {}",
                                            LOG_PREFIX, fileImportId, batchNumber, totalProcessed, totalExpected);
                                    return Uni.createFrom().failure(new IllegalStateException("Over-processed rows detected"));
                                }
                                return Uni.createFrom().voidItem();
                            });
                }, maxRetryAttempts)
                .onFailure().invoke(throwable -> log.error(
                        "{} ImportId {} batchNumber {} Error checking import completion",
                        LOG_PREFIX, fileImportId, batchNumber, throwable));
    }

    protected Uni<Integer> processSuccessRecords(ImportStatusEvent event, Long fileImportId) {
        if (event.getSuccesses() == null || event.getSuccesses().isEmpty()) {
            return Uni.createFrom().item(0);
        }

        List<FileImportSuccess> successes = new ArrayList<>();
        for (ImportStatusEvent.ImportSuccessRecord successRecord : event.getSuccesses()) {
            FileImportSuccess success = FileImportSuccess.builder()
                    .tenantId(event.getTenantId())
                    .fileImportId(fileImportId)
                    .rowNumber(successRecord.getRowNumber())
                    .entityId(successRecord.getEntityId())
                    .entityProperties(Optional.ofNullable(successRecord.getEntityProperties()).orElse(new HashMap<>()))
                    .relationMetadata(successRecord.getRelationMetadata())
                    .rawResponse("")
                    .batchNumber(event.getBatchNumber())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            successes.add(success);
        }

        return fileImportSuccessService.insert(successes)
                .map(ok -> successes.size());
    }

    protected Uni<Integer> processFailureRecords(ImportStatusEvent event, Long fileImportId) {
        if (event.getFailures() == null || event.getFailures().isEmpty()) {
            return Uni.createFrom().item(0);
        }

        List<FileImportError> importErrors = new ArrayList<>();
        for (ImportStatusEvent.ImportFailureRecord failureRecord : event.getFailures()) {
            FileImportError error = FileImportError.builder()
                    .tenantId(event.getTenantId())
                    .fileImportId(fileImportId)
                    .rowNumber(failureRecord.getRowNumber())
                    .errorMsg(failureRecord.getErrorMessage())
                    .requestBody(failureRecord.getRawRequestBody())
                    .responseBody(failureRecord.getResponseBody())
                    .batchNumber(event.getBatchNumber())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            importErrors.add(error);
        }

        return fileImportErrorService.insert(importErrors)
                .map(ok -> importErrors.size());
    }

    protected Uni<Void> sendNotification(FileImport fileImport, String fromStatus, String toStatus) {
        return notificationUtil.sendFileImportStatusNotification(fileImport, fromStatus, toStatus);
    }
}