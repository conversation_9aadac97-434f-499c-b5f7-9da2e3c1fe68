package com.tripudiotech.migration.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.base.event.ImportStatusEvent;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.repository.FileImportRepository;
import com.tripudiotech.migration.service.FileImportErrorService;
import com.tripudiotech.migration.service.FileImportSuccessService;
import com.tripudiotech.migration.util.NotificationUtil;
import io.smallrye.common.annotation.NonBlocking;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.ActivateRequestContext;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.eclipse.microprofile.reactive.messaging.Acknowledgment;
import org.eclipse.microprofile.reactive.messaging.Incoming;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Kafka consumer for processing messages from the import status dead letter queue
 */
@Slf4j
@ApplicationScoped
public class ImportStatusDLQConsumer {

    private static final String LOG_PREFIX = "[ImportStatusDLQConsumer]";
    private static final String RETRY_COUNT_HEADER = "x-retry-count";
    private static final int MAX_RETRY_ATTEMPTS = 5;

    @Inject
    ObjectMapper objectMapper;

    @Inject
    FileImportRepository fileImportRepository;

    @Inject
    FileImportErrorService fileImportErrorService;

    @Inject
    FileImportSuccessService fileImportSuccessService;

    @Inject
    NotificationUtil notificationUtil;

    @Inject
    ImportStatusHandler importStatusHandler;

    @Incoming("import-status-in-dlq")
    @Acknowledgment(Acknowledgment.Strategy.POST_PROCESSING)
    @NonBlocking
    @ActivateRequestContext
    public Uni<Void> consume(ConsumerRecord<String, String> record) {
        log.info("{} Received DLQ message: {}", LOG_PREFIX, record.value());

        try {
            ImportStatusEvent event = objectMapper.readValue(record.value(), ImportStatusEvent.class);
            AtomicInteger retryCount = new AtomicInteger(getRetryCount(record));

            if (retryCount.get() > MAX_RETRY_ATTEMPTS) {
                log.warn("{} Message exceeded maximum retry attempts ({}). Ignoring message for fileImportId: {}, batchNumber: {}",
                        LOG_PREFIX, MAX_RETRY_ATTEMPTS, event.getFileImportId(), event.getBatchNumber());
                return handleMaxRetriesExceeded(event);
            }

            return importStatusHandler.updateImportStatus(event)
                    .invoke(() -> {
                        log.info("{} Successfully reprocessed DLQ message for fileImportId: {}, batchNumber: {}, retryCount: {}",
                                LOG_PREFIX, event.getFileImportId(), event.getBatchNumber(), retryCount);
                    })
                    .onFailure()
                    .invoke(throwable -> {
                        log.error("{} Failed to reprocess DLQ message for fileImportId: {}, batchNumber: {}, retryCount: {}",
                                LOG_PREFIX, event.getFileImportId(), event.getBatchNumber(), retryCount, throwable);

                        // Add retry count header to the message
                        Header retryHeader = record.headers().lastHeader("x-retry-count");
                        if (retryHeader != null) {
                            try {
                                retryCount.set(Integer.parseInt(new String(retryHeader.value(), StandardCharsets.UTF_8)) + 1);
                            } catch (NumberFormatException e) {
                                log.warn("{} Invalid retry count header value", LOG_PREFIX);
                            }
                        }
                        record.headers().add("x-retry-count", String.valueOf(retryCount.get()).getBytes(StandardCharsets.UTF_8));
                    });
        } catch (Exception e) {
            log.error("{} Error deserializing DLQ message", LOG_PREFIX, e);
            return Uni.createFrom().failure(e);
        }
    }

    private int getRetryCount(ConsumerRecord<String, String> record) {
        Header retryHeader = record.headers().lastHeader(RETRY_COUNT_HEADER);
        if (retryHeader != null) {
            String retryCountStr = new String(retryHeader.value(), StandardCharsets.UTF_8);
            try {
                return Integer.parseInt(retryCountStr);
            } catch (NumberFormatException e) {
                log.warn("{} Invalid retry count header value: {}", LOG_PREFIX, retryCountStr);
            }
        }
        return 1; // First retry if no header found
    }

    private Uni<Void> handleMaxRetriesExceeded(ImportStatusEvent event) {
        return fileImportRepository.findByIdReadOnly(event.getFileImportId())
                .onItem().ifNotNull().transformToUni(fileImport -> {
                    if (fileImport == null) {
                        return Uni.createFrom().voidItem();
                    }

                    log.warn("Import with id {} MaxRetriesExceeded {}", event.getFileImportId(), MAX_RETRY_ATTEMPTS);

                    // If already in final state, just create error record
                    if (fileImport.getStatus() == FileImport.Status.COMPLETED ||
                        fileImport.getStatus() == FileImport.Status.ERROR) {
                        return Uni.createFrom().voidItem();
                    }

                    // Get current counts
                    return Uni.combine().all().unis(
                                    fileImportSuccessService.countByFileImportId(fileImport.getId()),
                                    fileImportErrorService.countByFileImportId(fileImport.getId())
                            ).asTuple()
                            .flatMap(counts -> {
                                // Update file import with final counts
                                fileImport.setStatus(FileImport.Status.COMPLETED);
                                fileImport.setCompletedAt(LocalDateTime.now());
                                fileImport.setEndTime(LocalDateTime.now());
                                fileImport.setProcessedRows((counts.getItem1() + counts.getItem2()));
                                fileImport.setSuccessRows(counts.getItem1());
                                fileImport.setFailedRows(counts.getItem2());
                                fileImport.setProcessedBatches(fileImport.getProcessedBatches() + 1);

                                return fileImportRepository.save(fileImport)
                                        .flatMap(updated -> notificationUtil.sendFileImportStatusNotification(
                                                updated,
                                                fileImport.getStatus().name(),
                                                FileImport.Status.COMPLETED.name()
                                        ));
                            });
                });
    }
}
