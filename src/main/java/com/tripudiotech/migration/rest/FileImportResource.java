/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.rest;

import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.dto.response.FileImportResponse;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.service.FileImportErrorService;
import com.tripudiotech.migration.service.FileImportExecutor;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.migration.service.FileImportSuccessService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.tripudiotech.base.constant.RequestConstants.DEFAULT_LIMIT;
import static com.tripudiotech.base.constant.RequestConstants.DEFAULT_OFFSET;
import static com.tripudiotech.base.constant.RequestConstants.LIMIT_REQUEST_PARAM;
import static com.tripudiotech.base.constant.RequestConstants.OFFSET_REQUEST_PARAM;

@Path("/import")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class FileImportResource extends RestResource {

    @Inject
    FileImportService fileImportService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    FileImportErrorService fileImportErrorService;

    @Inject
    FileImportExecutor fileImportExecutor;

    @Inject
    FileImportSuccessService fileImportSuccessService;

    @Inject
    Validator validator;

    @POST
    @Path("/manual-trigger")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Void> manualTriggerProcessFileImport() {
        return fileImportExecutor.process();
    }

    @POST
    @APIResponseSchema(value = FileImportResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Operation(summary = "Upload the file to import")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> upload(
            UploadFormDataRequest formData
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        //For backward compatible
        if (formData.hasAdvanced()) {
            //Added here to make sure valid Json schema
            ParseSetting parsingAndValidationReq = JsonUtil.parseToObject(tenantId, formData.getParsing(), ParseSetting.class);
            Set<ConstraintViolation<ParseSetting>> violations = validator.validate(parsingAndValidationReq);

            if (!violations.isEmpty())
                throw new BadRequestException(tenantId, violations.parallelStream().map(ConstraintViolation::getMessage).collect(Collectors.joining(",\n")));
        }

        return fileImportService.uploadFile(
                        this.tenantId,
                        userInformation,
                        formData
                )
                .map(fileImport ->
                        Response.status(Status.CREATED)
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get list uploaded file by current user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getList(
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit,
            @QueryParam("status") String importStatus,
            @QueryParam("entityTypes") Set<String> entityTypes
    ) {

        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.getListFileImport(
                        this.tenantId,
                        userInformation,
                        entityTypes,
                        importStatus,
                        offset,
                        limit
                )
                .map(fileImport ->
                        Response.ok()
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @Path("/{id}")
    @APIResponseSchema(value = FileImportResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get uploaded file by id and current user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getById(@PathParam("id") Long fileId) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.getFileImport(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        userInformation)
                .map(fileImport ->
                        Response.ok()
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @Path("/{id}/fail")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get failed validation row in the file")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getFileValidateFailed(
            @PathParam("id") Long fileId,
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportErrorService.getFileImportErrorsByFileId(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        offset,
                        limit, userInformation)
                .map(result ->
                        Response.ok()
                                .entity(result)
                                .build()
                );
    }

    @GET
    @Path("/{id}/success")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get success imported entity")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getImportSuccess(
            @PathParam("id") Long fileId,
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportSuccessService.getFileImportSuccessByFileId(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        offset,
                        limit, userInformation
                )
                .map(result ->
                        Response.ok()
                                .entity(result)
                                .build()
                );
    }

    @POST
    @Path("/{id}/cancel")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get failed validation row in the file")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Response> cancelImportJob(
            @PathParam("id") Long fileId
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.cancelImportJob(
                        this.tenantId,
                        userInformation,
                        fileId
                )
                .map(result ->
                        Response.ok().build()
                );
    }
}
