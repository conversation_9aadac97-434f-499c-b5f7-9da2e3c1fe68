/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.rest;

import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.dto.request.UploadFormDataRequest;
import com.tripudiotech.migration.dto.response.FileImportResponse;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.service.FileImportErrorService;
import com.tripudiotech.migration.service.FileImportExecutor;
import com.tripudiotech.migration.service.FileImportService;
import com.tripudiotech.migration.service.FileImportSuccessService;

import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.type.TypeReference;

import static com.tripudiotech.base.constant.RequestConstants.DEFAULT_LIMIT;
import static com.tripudiotech.base.constant.RequestConstants.DEFAULT_OFFSET;
import static com.tripudiotech.base.constant.RequestConstants.LIMIT_REQUEST_PARAM;
import static com.tripudiotech.base.constant.RequestConstants.OFFSET_REQUEST_PARAM;

@Path("/import")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
@Slf4j
public class FileImportResource extends RestResource {

    @Inject
    FileImportService fileImportService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    FileImportErrorService fileImportErrorService;

    @Inject
    FileImportExecutor fileImportExecutor;

    @Inject
    FileImportSuccessService fileImportSuccessService;

    @Inject
    Validator validator;

    @POST
    @Path("/manual-trigger")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Void> manualTriggerProcessFileImport() {
        return fileImportExecutor.process();
    }

    @POST
    @APIResponseSchema(value = FileImportResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Operation(summary = "Upload the file to import")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> upload(
            UploadFormDataRequest formData
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        // Comprehensive validation of the upload request
        try {
            validateUploadRequest(formData);
        } catch (Exception e) {
            log.error("Upload validation failed for tenant: {}, user: {}, error: {}",
                     tenantId, userInformation.getEmail(), e.getMessage());
            throw new BadRequestException(tenantId, e.getMessage());
        }

        //For backward compatible
        if (formData.hasAdvanced()) {
            //Added here to make sure valid Json schema
            ParseSetting parsingAndValidationReq = JsonUtil.parseToObject(tenantId, formData.getParsing(), ParseSetting.class);
            Set<ConstraintViolation<ParseSetting>> violations = validator.validate(parsingAndValidationReq);

            if (!violations.isEmpty())
                throw new BadRequestException(tenantId, violations.parallelStream().map(ConstraintViolation::getMessage).collect(Collectors.joining(",\n")));
        }

        return fileImportService.uploadFile(
                        this.tenantId,
                        userInformation,
                        formData
                )
                .map(fileImport ->
                        Response.status(Status.CREATED)
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get list uploaded file by current user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getList(
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit,
            @QueryParam("status") String importStatus,
            @QueryParam("entityTypes") Set<String> entityTypes
    ) {

        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.getListFileImport(
                        this.tenantId,
                        userInformation,
                        entityTypes,
                        importStatus,
                        offset,
                        limit
                )
                .map(fileImport ->
                        Response.ok()
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @Path("/{id}")
    @APIResponseSchema(value = FileImportResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get uploaded file by id and current user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getById(@PathParam("id") Long fileId) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.getFileImport(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        userInformation)
                .map(fileImport ->
                        Response.ok()
                                .entity(fileImport)
                                .build()
                );
    }

    @GET
    @Path("/{id}/fail")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get failed validation row in the file")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getFileValidateFailed(
            @PathParam("id") Long fileId,
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportErrorService.getFileImportErrorsByFileId(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        offset,
                        limit, userInformation)
                .map(result ->
                        Response.ok()
                                .entity(result)
                                .build()
                );
    }

    @GET
    @Path("/{id}/success")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get success imported entity")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> getImportSuccess(
            @PathParam("id") Long fileId,
            @QueryParam(OFFSET_REQUEST_PARAM) @DefaultValue(DEFAULT_OFFSET) Integer offset,
            @QueryParam(LIMIT_REQUEST_PARAM) @DefaultValue(DEFAULT_LIMIT) Integer limit
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportSuccessService.getFileImportSuccessByFileId(
                        this.tenantId,
                        userInformation.getEmail(),
                        fileId,
                        offset,
                        limit, userInformation
                )
                .map(result ->
                        Response.ok()
                                .entity(result)
                                .build()
                );
    }

    @POST
    @Path("/{id}/cancel")
    @APIResponseSchema(value = PageResponse.class)
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "Get failed validation row in the file")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    public Uni<Response> cancelImportJob(
            @PathParam("id") Long fileId
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService()
                        .getCurrentUserInformation();

        return fileImportService.cancelImportJob(
                        this.tenantId,
                        userInformation,
                        fileId
                )
                .map(result ->
                        Response.ok().build()
                );
    }

    /**
     * Comprehensive validation for upload requests
     */
    private void validateUploadRequest(UploadFormDataRequest formData) {
        // Basic null checks
        if (formData == null) {
            throw new IllegalArgumentException("Upload request cannot be null");
        }

        if (formData.getFile() == null) {
            throw new IllegalArgumentException("File cannot be null");
        }

        // Validate import type
        if (formData.getImportType() == null) {
            throw new IllegalArgumentException("Import type must be specified");
        }

        // Validate import type specific requirements
        validateImportTypeRequirements(formData);

        // Validate data mapping if provided
        if (formData.getDataMapping() != null && !formData.getDataMapping().trim().isEmpty()) {
            validateDataMapping(formData);
        }

        // Validate parsing settings if provided
        if (formData.getParsing() != null && !formData.getParsing().trim().isEmpty()) {
            validateParsingSettings(formData);
        }
    }

    /**
     * Validate import type specific requirements
     */
    private void validateImportTypeRequirements(UploadFormDataRequest formData) {
        switch (formData.getImportType()) {
            case SINGLE_DATA_TYPE:
                if (formData.getEntityType() == null || formData.getEntityType().trim().isEmpty()) {
                    throw new IllegalArgumentException("Entity type must be specified for single data type import");
                }
                break;
            case MULTIPLE_DATA_TYPES:
                if (formData.getDataTypeColumn() == null || formData.getDataTypeColumn().trim().isEmpty()) {
                    throw new IllegalArgumentException("Data type column must be specified for multiple data types import");
                }
                break;
            default:
                throw new IllegalArgumentException("Invalid import type: " + formData.getImportType());
        }
    }

    /**
     * Validate data mapping structure and content
     */
    private void validateDataMapping(UploadFormDataRequest formData) {
        try {
            // Parse JSON string to List<DataMapping> using JsonUtil.OBJECT_MAPPER
            List<DataMapping> mappings = JsonUtil.OBJECT_MAPPER.readValue(
                formData.getDataMapping(),
                new TypeReference<List<DataMapping>>() {}
            );

            if (mappings == null || mappings.isEmpty()) {
                throw new IllegalArgumentException("Data mapping cannot be empty when provided");
            }

            // Validate each mapping
            for (int i = 0; i < mappings.size(); i++) {
                DataMapping mapping = mappings.get(i);
                validateSingleDataMapping(mapping, i);
            }

            // Validate mapping consistency
            validateMappingConsistency(mappings, formData);

        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new IllegalArgumentException("Invalid data mapping JSON format: " + e.getMessage());
        }
    }

    /**
     * Validate individual data mapping
     */
    private void validateSingleDataMapping(DataMapping mapping, int index) {
        if (mapping == null) {
            throw new IllegalArgumentException("Data mapping at index " + index + " cannot be null");
        }

        if (mapping.getColumnName() == null || mapping.getColumnName().trim().isEmpty()) {
            throw new IllegalArgumentException("Column name cannot be empty at mapping index " + index);
        }

        if (mapping.getTarget() == null || mapping.getTarget().trim().isEmpty()) {
            throw new IllegalArgumentException("Target cannot be empty at mapping index " + index);
        }

        if (mapping.getType() == null) {
            throw new IllegalArgumentException("Mapping type cannot be null at mapping index " + index);
        }

        // Validate ObjectType
        validateObjectType(mapping.getType(), index);

        // Validate target format for specific types
        validateTargetFormat(mapping, index);
    }

    /**
     * Validate ObjectType values
     */
    private void validateObjectType(ObjectType type, int index) {
        // Check if the ObjectType is valid for import operations
        Set<ObjectType> validImportTypes = Set.of(
            ObjectType.ATTRIBUTE,
            ObjectType.USER_ID,
            ObjectType.FROM_EXISTING_ENTITY,
            ObjectType.RELATION,
            ObjectType.ASSEMBLY,
            ObjectType.COMPONENT,
            ObjectType.LEVEL_BASED_BOM,
            ObjectType.IDENTIFIER,
            ObjectType.LIFE_CYCLE
        );

        if (!validImportTypes.contains(type)) {
            throw new IllegalArgumentException("Invalid mapping type '" + type + "' at index " + index +
                ". Valid types are: " + validImportTypes);
        }
    }

    /**
     * Validate target format for specific mapping types
     */
    private void validateTargetFormat(DataMapping mapping, int index) {
        ObjectType type = mapping.getType();
        String target = mapping.getTarget();

        switch (type) {
            case FROM_EXISTING_ENTITY:
                // Target should be in format "EntityType.attributeName"
                if (!target.contains(".") || target.split("\\.").length != 2) {
                    throw new IllegalArgumentException("FROM_EXISTING_ENTITY target must be in format 'EntityType.attributeName' at index " + index);
                }
                break;
            case USER_ID:
                // Target should typically be "email" or similar user identifier
                if (!target.matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
                    throw new IllegalArgumentException("USER_ID target must be a valid attribute name at index " + index);
                }
                break;
            case ATTRIBUTE:
                // Target should be a valid attribute name
                if (!target.matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
                    throw new IllegalArgumentException("ATTRIBUTE target must be a valid attribute name at index " + index);
                }
                break;
            case RELATION:
            case ASSEMBLY:
            case COMPONENT:
            case LEVEL_BASED_BOM:
            case IDENTIFIER:
            case LIFE_CYCLE:
                // These types have specific validation rules handled elsewhere
                // Basic target validation - should not be empty (already checked above)
                break;
            default:
                throw new IllegalArgumentException("Unsupported mapping type '" + type + "' at index " + index);
        }
    }

    /**
     * Validate mapping consistency and business rules
     */
    private void validateMappingConsistency(List<DataMapping> mappings, UploadFormDataRequest formData) {
        // Check for duplicate column mappings
        Set<String> usedColumns = new LinkedHashSet<>();
        Set<String> duplicateColumns = new LinkedHashSet<>();

        for (DataMapping mapping : mappings) {
            String columnName = mapping.getColumnName().toUpperCase();
            if (usedColumns.contains(columnName)) {
                duplicateColumns.add(columnName);
            }
            usedColumns.add(columnName);
        }

        if (!duplicateColumns.isEmpty()) {
            throw new IllegalArgumentException("Duplicate column mappings found: " + duplicateColumns);
        }

        // Validate USER_ID import requirements
        validateUserIdImportRequirements(mappings);

        // Validate BOM import requirements
        validateBomImportRequirements(mappings);

        // Validate multiple data types requirements
        if (formData.getImportType() == ImportType.MULTIPLE_DATA_TYPES) {
            validateMultipleDataTypesRequirements(mappings, formData);
        }
    }

    /**
     * Validate USER_ID import specific requirements
     */
    private void validateUserIdImportRequirements(List<DataMapping> mappings) {
        boolean hasUserId = mappings.stream().anyMatch(m -> m.getType() == ObjectType.USER_ID);
        boolean hasFromExistingEntity = mappings.stream().anyMatch(m -> m.getType() == ObjectType.FROM_EXISTING_ENTITY);

        if (hasUserId && !hasFromExistingEntity) {
            throw new IllegalArgumentException("USER_ID import requires at least one FROM_EXISTING_ENTITY mapping to identify the company");
        }

        if (hasUserId) {
            long userIdCount = mappings.stream().filter(m -> m.getType() == ObjectType.USER_ID).count();
            if (userIdCount > 1) {
                throw new IllegalArgumentException("Only one USER_ID mapping is allowed per import");
            }
        }
    }

    /**
     * Validate BOM import specific requirements
     */
    private void validateBomImportRequirements(List<DataMapping> mappings) {
        boolean hasAssembly = mappings.stream().anyMatch(m -> m.getType() == ObjectType.ASSEMBLY);
        boolean hasComponent = mappings.stream().anyMatch(m -> m.getType() == ObjectType.COMPONENT);
        boolean hasLevelBasedBom = mappings.stream().anyMatch(m -> m.getType() == ObjectType.LEVEL_BASED_BOM);

        if (hasLevelBasedBom && (hasAssembly || hasComponent)) {
            throw new IllegalArgumentException("LEVEL_BASED_BOM cannot be mixed with ASSEMBLY or COMPONENT mappings");
        }

        if ((hasAssembly || hasComponent) && !(hasAssembly && hasComponent)) {
            throw new IllegalArgumentException("BOM import requires both ASSEMBLY and COMPONENT mappings");
        }
    }

    /**
     * Validate multiple data types import requirements
     */
    private void validateMultipleDataTypesRequirements(List<DataMapping> mappings, UploadFormDataRequest formData) {
        String dataTypeColumn = formData.getDataTypeColumn();

        // Check if data type column is mapped
        boolean dataTypeColumnMapped = mappings.stream()
            .anyMatch(m -> m.getColumnName().equalsIgnoreCase(dataTypeColumn));

        if (dataTypeColumnMapped) {
            throw new IllegalArgumentException("Data type column '" + dataTypeColumn + "' should not be included in data mappings for multiple data types import");
        }
    }

    /**
     * Validate parsing settings
     */
    private void validateParsingSettings(UploadFormDataRequest formData) {
        try {
            ParseSetting parseSetting = JsonUtil.parseToObject(tenantId, formData.getParsing(), ParseSetting.class);
            Set<ConstraintViolation<ParseSetting>> violations = validator.validate(parseSetting);

            if (!violations.isEmpty()) {
                String errorMessage = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", "));
                throw new IllegalArgumentException("Invalid parsing settings: " + errorMessage);
            }
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new IllegalArgumentException("Invalid parsing settings JSON format: " + e.getMessage());
        }
    }
}
