package com.tripudiotech.migration.service.processor.file.handler;

import com.tripudiotech.base.dto.request.BomCreateRequest;
import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.RelationType;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.migration.util.DelimiterUtils;
import com.tripudiotech.migration.util.FileUtils;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Handler for processing different types of column definitions in import files.
 * This class separates the logic for handling attributes, lifecycles, relations, and BOM data.
 */
@Slf4j
@ApplicationScoped
public class ColumnDefinitionHandler {

    public static final String STRING_ARRAY = "STRING_ARRAY";
    public static final String OWNER = "OWNER";
    public static final char DEFAULT_DELIMITER = ',';

    @Inject
    private LifecycleHandler lifecycleHandler;

    @Inject
    private EntityService entityService;

    /**
     * Process a column definition based on its type
     */
    public Uni<Void> processColumnDefinition(String tenantId, String bearToken, FileImport fileImport, String columnValue,
                                             ColumnDefinition columnDefinition, ParseSetting parseSetting,
                                             EntitySchema entitySchema, RowExtractedData rowExtractedData) {
        return switch (ObjectType.valueOf(columnDefinition.getFieldType().toUpperCase())) {
            case ATTRIBUTE -> handleAttributeData(columnDefinition, columnValue, parseSetting, rowExtractedData);
            case LIFE_CYCLE ->
                    lifecycleHandler.handleLifecycleData(tenantId, bearToken, fileImport, columnValue, rowExtractedData, columnDefinition);
            case RELATION ->
                    handleRelationData(fileImport, columnDefinition, columnValue, entitySchema, rowExtractedData);
            case ASSEMBLY, COMPONENT -> handleBomData(columnDefinition, columnValue, rowExtractedData);
            case FROM_EXISTING_ENTITY ->
                    handleFromExistingEntityData(tenantId, bearToken, columnDefinition, columnValue, rowExtractedData);
            case USER_ID -> handleUserIdData(columnDefinition, columnValue, rowExtractedData);
            default -> handleAccessorData(fileImport, columnDefinition, columnValue, entitySchema, rowExtractedData);
        };
    }

    private Uni<Void> handleUserIdData(ColumnDefinition columnDefinition, String columnValue, RowExtractedData rowExtractedData) {
        if (StringUtils.isNotBlank(columnValue)) {
            log.info("Extracted USER_ID. FileImport: {}, AgentId: {}", rowExtractedData.getRowNumber(), columnValue);
            rowExtractedData.setAgentId(columnValue.trim());
            rowExtractedData.setObjectType(ObjectType.USER_ID);
        }
        return Uni.createFrom().voidItem();
    }

    /**
     * Handle attribute data
     */
    public Uni<Void> handleAttributeData(ColumnDefinition columnDefinition, String columnValue,
                                         ParseSetting parseSetting, RowExtractedData rowExtractedData) {
        if (StringUtils.isNotBlank(columnValue)) {
            if (STRING_ARRAY.equalsIgnoreCase(columnDefinition.getTargetValueType())) {
                char delimiter = parseSetting != null && parseSetting.getMultiListDelimiter() != null ?
                        parseSetting.getMultiListDelimiter().getValue() : DEFAULT_DELIMITER;
                String[] values = DelimiterUtils.splitByDelimiter(columnValue, delimiter);
                rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), values);
            } else {
                if (columnDefinition.getMaxLength() != null && columnValue.length() > columnDefinition.getMaxLength()) {
                    if (parseSetting != null && LengthValidation.TRUNCATE.equals(parseSetting.getLengthValidation())) {
                        columnValue = StringUtils.truncate(columnValue, columnDefinition.getMaxLength());
                    }
                }
                rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), columnValue);
            }
        }

        return Uni.createFrom().voidItem();
    }

    /**
     * Handle BOM data (assembly and component)
     */
    public Uni<Void> handleBomData(ColumnDefinition columnDefinition, String columnValue,
                                   RowExtractedData rowExtractedData) {
        if (StringUtils.isNotBlank(columnValue)) {
            BomCreateRequest.Identifier identifier = new BomCreateRequest.Identifier();
            identifier.setIdType(columnDefinition.getTargetField());
            identifier.setIdValue(columnValue);
            switch (ObjectType.valueOf(columnDefinition.getFieldType().toUpperCase())) {
                case ASSEMBLY -> rowExtractedData.setAssembly(identifier);
                case COMPONENT -> rowExtractedData.setComponent(identifier);
            }
        }

        return Uni.createFrom().voidItem();
    }

    /**
     * Handle FROM_EXISTING_ENTITY data
     * <p>
     * This method processes columns with the FROM_EXISTING_ENTITY mapping type.
     * It extracts the entity type and field name from the target (e.g., "EngineeringName.name")
     * and stores this information in the RowExtractedData for later use.
     *
     * @param tenantId
     * @param bearerToken
     * @param columnDefinition The column definition with target format EntityType.fieldName
     * @param columnValue      The value from the column that identifies the existing entity
     * @param rowExtractedData The row data to update with existing entity information
     * @return A completed Uni
     */
    public Uni<Void> handleFromExistingEntityData(String tenantId, String bearerToken, ColumnDefinition columnDefinition, String columnValue,
                                                  RowExtractedData rowExtractedData) {
        if (StringUtils.isNotBlank(columnValue)) {
            // Parse the target field (format: EntityType.fieldName)
            String[] targetParts = columnDefinition.getTargetField().split("\\.");
            if (targetParts.length != 2) {
                log.error("Invalid target format for FROM_EXISTING_ENTITY: {}. Expected format: EntityType.fieldName",
                        columnDefinition.getTargetField());
                return Uni.createFrom().failure(new IllegalArgumentException(
                        "Invalid target format for FROM_EXISTING_ENTITY: " + columnDefinition.getTargetField()));
            }

            String entityType = targetParts[0]; // e.g., "EngineeringName"
            String fieldName = targetParts[1];  // e.g., "name"


            log.debug("FROM_EXISTING_ENTITY mapping: entityType: {} fieldName: {} columnValue: {}", entityType, fieldName, columnValue);

            // Store the existing entity information
            rowExtractedData.setUseExistingEntity(true);
            rowExtractedData.setExistingEntityType(entityType);
            rowExtractedData.setExistingEntityField(fieldName);
            rowExtractedData.setExistingEntityValue(columnValue);

        }
        return Uni.createFrom().voidItem();
    }

    /**
     * Handle accessor data
     */
    public Uni<Void> handleAccessorData(FileImport fileImport, ColumnDefinition columnDefinition,
                                        String columnValue, EntitySchema entitySchema, RowExtractedData rowExtractedData) {
        log.info("Extracted ACCESSOR. FileImport: {}, Role: {}, AgentId: {}", fileImport.getId(), columnDefinition, columnValue);
        rowExtractedData.getPermission().setRole(columnDefinition.getFieldValue());
        rowExtractedData.getPermission().setAgentId(columnValue.trim());

        if (OWNER.equalsIgnoreCase(rowExtractedData.getPermission().getRole())) {
            String toEntityType = entitySchema.getRelationTypes().stream()
                    .filter(rel -> rel.getName().equalsIgnoreCase(DBConstants.RELATION_OWNED_BY))
                    .map(RelationType::getToEntityType)
                    .findFirst()
                    .orElse(null);

            // Add OWNER relation
            rowExtractedData.addRelation(DBConstants.RELATION_OWNED_BY, columnValue, null, toEntityType);
        }
        return Uni.createFrom().voidItem();
    }

    /**
     * Handle relation data
     */
    public Uni<Void> handleRelationData(
            FileImport fileImport,
            ColumnDefinition columnDefinition,
            String columnValue,
            EntitySchema entitySchema,
            RowExtractedData rowExtractedData
    ) {
        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }

        String relationNameUpper;
        String targetEntityType;
        String attributeName;

        // Regular relation handling
        var extractedDataRelations = FileUtils.extractDataRelation(columnDefinition.getFieldValue());
        if (extractedDataRelations == null) {
            log.error("relation is in invalid format");
            return Uni.createFrom().voidItem();
        }

        relationNameUpper = extractedDataRelations.get(0);
        targetEntityType = extractedDataRelations.get(1);
        attributeName = extractedDataRelations.get(2);

        log.info(
                "Extracted RELATION. FileImport: {}, RelationName: {}, TargetEntity: {} Attribute: {}, Value: {}",
                fileImport.getId(),
                relationNameUpper,
                targetEntityType,
                attributeName,
                columnValue
        );

        // Find the target entity type for this relation
        entitySchema.getRelationTypes().stream().filter(relationType ->
                        relationType.getName().equalsIgnoreCase(relationNameUpper))
                .forEach(relationType ->
                        log.info("From {} to {}", relationType.getFromEntityType(), relationType.getToEntityType()));

        String entityType =
                entitySchema.getRelationTypes().stream()
                        .filter(rel -> rel.getName().equalsIgnoreCase(relationNameUpper))
                        .filter(
                                rel ->
                                        rel.getFromEntityType().equalsIgnoreCase(targetEntityType)
                                        || rel.getToEntityType().equalsIgnoreCase(targetEntityType)
                                        || rel.getFromEntityType().equalsIgnoreCase(fileImport.getEntityType())
                                        || rel.getToEntityType().equalsIgnoreCase(fileImport.getEntityType())
                        )
                        .map(
                                rel -> {
                                    if (rel.getFromEntityType().equalsIgnoreCase(fileImport.getEntityType())) {
                                        return rel.getFromEntityType();
                                    }
                                    return rel.getToEntityType();
                                })
                        .findFirst()
                        .orElse(null);

        if (Objects.isNull(entityType)) {
            log.warn(
                    "Could not find relation [{}] for entity type [{}]",
                    relationNameUpper,
                    entitySchema.getEntityType().getName());
            return Uni.createFrom().voidItem();
        }

        // If user already specific id, then we don't need to additional query
        if (SysRoot.Fields.id.equalsIgnoreCase(attributeName)) {
            // Add relation with ID directly provided
            rowExtractedData.addRelation(relationNameUpper, columnValue, null, targetEntityType);
            return Uni.createFrom().voidItem();
        }

        // Store the relation ID
        return entityService
                .getUniqueEntityIdByAttributeWithCache(
                        fileImport.getTenantId(),
                        fileImport.getRequestedByEmail(),
                        targetEntityType,
                        attributeName,
                        columnValue.trim().replaceAll(StringUtils.SPACE, "_"),
                        columnValue)
                .flatMap(
                        optionalCorrespondingId -> {
                            if (optionalCorrespondingId.isEmpty()) {
                                log.error(
                                        "Unable to find entity id of entity [{}] where [{}] = [{}]. Skip processing this relation of row [{}]",
                                        targetEntityType,
                                        attributeName,
                                        columnValue,
                                        rowExtractedData.getRowNumber());
                                return Uni.createFrom().voidItem();
                            }

                            var entityIdForRelation = optionalCorrespondingId.get();
                            // Add relation with entity name and ID
                            rowExtractedData.addRelation(relationNameUpper, entityIdForRelation, columnValue, targetEntityType);

                            return Uni.createFrom().voidItem();
                        });
    }
}