# File Import Validation Enhancement

## Overview

The file import validation has been enhanced to provide comprehensive validation for upload requests, preventing invalid mappings and import types before processing begins.

## Architecture

### FileImportValidator

A dedicated validator class (`FileImportValidator`) has been created to handle all validation logic, following the single responsibility principle and making the code more maintainable and testable.

### Location
- **Validator Class**: `src/main/java/com/tripudiotech/migration/validation/FileImportValidator.java`
- **Resource Class**: `src/main/java/com/tripudiotech/migration/rest/FileImportResource.java`
- **Tests**: `src/test/java/com/tripudiotech/migration/validation/FileImportValidatorTest.java`

## Validation Features

### 1. Basic Request Validation
- **Null checks**: Validates that request and file are not null
- **Import type validation**: Ensures import type is specified and valid

### 2. Import Type Specific Validation
- **SINGLE_DATA_TYPE**: Requires entity type to be specified
- **MULTIPLE_DATA_TYPES**: Requires data type column to be specified

### 3. Data Mapping Validation

#### JSON Structure Validation
- Validates JSON format and structure
- Ensures mapping array is not empty when provided
- Validates individual mapping objects

#### Individual Mapping Validation
- **Column name**: Cannot be null or empty
- **Target**: Cannot be null or empty  
- **Type**: Must be a valid ObjectType

#### ObjectType Validation
Valid import types include:
- `ATTRIBUTE`
- `USER_ID`
- `FROM_EXISTING_ENTITY`
- `RELATION`
- `ASSEMBLY`
- `COMPONENT`
- `LEVEL_BASED_BOM`
- `IDENTIFIER`
- `LIFE_CYCLE`

#### Target Format Validation
- **FROM_EXISTING_ENTITY**: Must be in format "EntityType.attributeName"
- **USER_ID**: Must be a valid attribute name (alphanumeric + underscore)
- **ATTRIBUTE**: Must be a valid attribute name (alphanumeric + underscore)

### 4. Business Rules Validation

#### Duplicate Column Prevention
- Prevents mapping the same column multiple times (case-insensitive)

#### USER_ID Import Rules
- USER_ID imports require at least one FROM_EXISTING_ENTITY mapping
- Only one USER_ID mapping is allowed per import

#### BOM Import Rules
- LEVEL_BASED_BOM cannot be mixed with ASSEMBLY or COMPONENT mappings
- Regular BOM imports require both ASSEMBLY and COMPONENT mappings

#### Multiple Data Types Rules
- Data type column should not be included in data mappings

### 5. Parsing Settings Validation
- Validates parsing settings JSON format
- Uses Bean Validation annotations for constraint validation

## Usage

### In FileImportResource

```java
@Inject
FileImportValidator fileImportValidator;

public Uni<Response> upload(UploadFormDataRequest formData) {
    try {
        fileImportValidator.validateUploadRequest(tenantId, formData);
    } catch (Exception e) {
        throw new BadRequestException(tenantId, e.getMessage());
    }
    // Continue with processing...
}
```

### Example Valid User Import Mapping

```json
[
  {
    "columnName": "EMAIL",
    "target": "email",
    "type": "USER_ID"
  },
  {
    "columnName": "COMPANY",
    "target": "Company.name",
    "type": "FROM_EXISTING_ENTITY"
  },
  {
    "columnName": "FIRST_NAME",
    "target": "firstName",
    "type": "ATTRIBUTE"
  }
]
```

### Example Valid BOM Import Mapping

```json
[
  {
    "columnName": "ASSEMBLY_ID",
    "target": "assemblyId",
    "type": "ASSEMBLY"
  },
  {
    "columnName": "COMPONENT_ID",
    "target": "componentId",
    "type": "COMPONENT"
  },
  {
    "columnName": "QUANTITY",
    "target": "quantity",
    "type": "ATTRIBUTE"
  }
]
```

## Error Messages

The validator provides clear, specific error messages:

- `"Upload request cannot be null"`
- `"File cannot be null"`
- `"Import type must be specified"`
- `"Entity type must be specified for single data type import"`
- `"Data type column must be specified for multiple data types import"`
- `"Invalid mapping type 'INVALID_TYPE' at index 0. Valid types are: [...]"`
- `"FROM_EXISTING_ENTITY target must be in format 'EntityType.attributeName' at index 0"`
- `"Duplicate column mappings found: [COLUMN1, COLUMN2]"`
- `"USER_ID import requires at least one FROM_EXISTING_ENTITY mapping to identify the company"`
- `"BOM import requires both ASSEMBLY and COMPONENT mappings"`

## Testing

Comprehensive test coverage includes:
- Null request validation
- Missing required fields
- Invalid JSON formats
- Invalid ObjectTypes
- Duplicate column mappings
- Business rule violations
- Valid mapping scenarios

## Benefits

1. **Early Error Detection**: Catches validation errors before processing begins
2. **Clear Error Messages**: Provides specific, actionable error messages
3. **Maintainable Code**: Separated validation logic into dedicated class
4. **Comprehensive Coverage**: Validates all aspects of import requests
5. **Business Rule Enforcement**: Ensures data integrity and consistency
6. **Testable**: Easy to unit test validation logic independently
